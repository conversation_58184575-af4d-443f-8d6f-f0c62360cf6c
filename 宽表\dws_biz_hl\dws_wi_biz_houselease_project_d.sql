with tzccz_zcczxm as (
select
	distinct
    t1.bsn_prj_wrd,
    t1.bsn_prj_id,
    t1.project_code, -- 项目编号
    t1.project_name, -- 项目名称
    t1.lessor_nm, -- 出租方名称
    t1.rent_lst_prc, -- 挂牌价格（元）
    t1.prj_prc_unit_dsc, -- 挂牌价格单位
    t1.rent_lit_tot_prc, -- 租金挂牌总价(元)
    t1.plan_lease_area, -- 拟出租面积(平方米)
    t1.esr_strt_dt, -- 披露开始日期
    t1.rnt_fee_est_yuan, -- 租金估价(元)
    t1.esr_end_dt, -- 披露结束日期
    t1.prj_sts_dsc, -- 项目状态
    t31.usr_nm as prj_pnp, -- 项目负责人
    t26.org_nm as prj_blng_dept, -- 项目所属部门
    t27.org_nm as prj_clm_dept_id, -- 项目认领部门
    t1.pick_mod_dsc, -- 遴选方式
    t1.ntw_bid_tp_dsc, -- 网络竞价类型
    t1.deposit_yuan, -- 保证金金额(元)
    t1.ast_cgy_dsc, -- 资产类别
    t1.nbjcqkmc, -- 内部决策情况名称
    t1.project_type_dsc, -- 项目类型
    t1.authorize_unit, -- 批准单位
    t1.est_unit_nm, -- 估价单位名称
    t1.est_file_avl_dt_beg, -- 估价文件有效期起始
    t1.est_file_avl_dt_ct_of, -- 估价文件有效期截止
    t1.is_exst_prty_rentw, -- 是否存在优先承租权
    t1.rnt_fee_prj_prc_rmrk, -- 租金挂牌价备注
    t1.pcolt_lhder_cnt, -- 拟征集承租方个数
    t1.rent_postulate, -- 承租方资格条件
    t1.mbsh_org, -- 会员机构
    t1.deal_mth_cd_dsc, -- 成交方式
    t1.use_rqmt_cd_dsc, -- 房屋使用用途
    t1.is_nal_hs_rent, -- 是否国有房屋出租
    t1.is_resp_val_dsc -- 0|统一计价；1|分别计价；2|面议
from dwd.dwd_ast_lease_prj_fct t1
LEFT JOIN  (
SELECT 
    id  --代码 
    ,usr_nm  --码值
FROM  std.std_bjhl_tuser_d  --用户管理
WHERE  dt='${dmp_day}') t31                          
ON t1.prj_pnp=t31.ID
INNER JOIN (
SELECT 
    id  ,--代码 
    org_nm  --码值
FROM std.std_bjhl_lborganization_d --组织机构
WHERE dt = '${dmp_day}' and org_nm LIKE '%市属%') t26
ON t1.prj_blng_dept = t26.id
LEFT JOIN (
SELECT 
    id  ,--代码 
    org_nm  --码值
FROM std.std_bjhl_lborganization_d --组织机构
WHERE dt = '${dmp_day}') t27
ON t1.prj_clm_dept_id = t27.id

where dt = '${dmp_day}' and t1.project_code is not NULL
),
tzccz_czfxx as (
SELECT 
    project_id,
    concat_ws(',', collect_set(cast(lessor_tp_dsc AS STRING))) AS lessor_tp_dsc, -- 出租方类型描述
    concat_ws(',', collect_set(cast(T2.ast_src_nm AS STRING))) AS ast_src_nm_2, -- 资产来源(2级)
    concat_ws(',', collect_set(cast(T3.ast_src_nm AS STRING))) AS ast_src_nm_3, -- 资产来源(3级)
    concat_ws(',', collect_set(cast(T4.ast_src_nm AS STRING))) AS ast_src_nm_4, -- 资产来源(4级)
    concat_ws(',', collect_set(cast(blng_grp AS STRING))) AS blng_grp,          -- 所属集团
    concat_ws(',', collect_set(cast(cty_contri_corp_lead_dept AS STRING))) AS cty_contri_corp_lead_dept, -- 国家出资企业或主管部门
    concat_ws(',', collect_set(cast(k1.lessor_nm AS STRING))) AS ast_src_entp_nm_1, -- 资产来源（企业1级）
    concat_ws(',', collect_set(cast(k2.lessor_nm AS STRING))) AS ast_src_entp_nm_2, -- 资产来源（企业2级）
    concat_ws(',', collect_set(cast(k3.lessor_nm AS STRING))) AS ast_src_entp_nm_3, -- 资产来源（企业3级）
    concat_ws(',', collect_set(cast(k4.lessor_nm AS STRING))) AS ast_src_entp_nm_4, -- 资产来源（企业4级）
    concat_ws(',', collect_set(cast(industry_tp_dsc AS STRING))) AS industry_tp_dsc, -- 所属行业类型描述
    concat_ws(',', collect_set(cast(ctc_psn AS STRING))) AS ctc_psn,                -- 联系人
    concat_ws(',', collect_set(cast(ctc_psn_phone AS STRING))) AS ctc_psn_phone,     -- 联系人手机号
    concat_ws(',', collect_set(cast(msg_addr AS STRING))) AS msg_addr,              -- 通讯地址
    concat_ws(',', collect_set(cast(contact_bank_account_nm AS STRING))) AS contact_bank_account_nm, -- 银行账户名称
    concat_ws(',', collect_set(cast(contact_bank_account_acc_no AS STRING))) AS contact_bank_account_acc_no, -- 银行账户账号
    concat_ws(',', collect_set(cast(opn_acc_bnk_nm AS STRING))) AS opn_acc_bnk_nm,   -- 开户银行名称
    concat_ws(',', collect_set(cast(opn_acc_bnk_subbr AS STRING))) AS opn_acc_bnk_subbr, -- 开户银行支行
    concat_ws(',', collect_set(cast(dep_bnk_bnk_cd AS STRING))) AS dep_bnk_bnk_cd,    -- 开户行联行号
    concat_ws(',', collect_set(cast(oasset_reg_org_dsc AS STRING))) AS oasset_reg_org_dsc,--国资监管机构描述
    concat_ws(',', collect_set(cast(custd_org_depdc_prov AS STRING))) AS custd_org_depdc_prov --监管机构属地(省)
FROM std.std_bjhl_tzccz_czfxx_d T1
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='1') T2 ON T2.ast_src_cd = T1.ast_src_2_cla
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='2') T3 ON T3.ast_src_cd = T1.ast_src_3_cla
LEFT JOIN (SELECT ast_src_cd, ast_src_nm FROM std.std_bjhl_tbjhl_zcly_d WHERE dt='${dmp_day}' AND lvl='3') T4 ON T4.ast_src_cd = T1.ast_src_4_cla
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k1 WHERE dt = '${dmp_day}'
) k1 ON T1.ast_src_entp_1_cla = k1.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k2 WHERE dt = '${dmp_day}'
) k2 ON T1.ast_src_entp_2_cla = k2.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k3 WHERE dt = '${dmp_day}'
) k3 ON T1.ast_src_entp_3_cla = k3.id
LEFT JOIN (
    SELECT id, lessor_nm FROM std.std_bjhl_tzccz_czfxx_d k4 WHERE dt = '${dmp_day}'
) k4 ON T1.ast_src_entp_4_cla = k4.id
WHERE T1.dt = '${dmp_day}' AND T1.project_id IS NOT NULL
GROUP BY project_id
),
tzccz_cjjl as (
 select 
    replace(replace(bsn_prj_wrd,'BJHL',''),'ZCCZ','') as bsn_prj_wrd,      --业务项目关键字
    lease_tm_legth,           -- 年 
    lease_tm_legth2,          -- 月 
    lease_tm_legth3,          -- 日
    sevly_valut_cd_dsc,
    lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30 as project_price_m, -- 挂牌价格（元/平米/月）
    lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365 as project_price_y, -- 挂牌价格（元/平米/年）   
    case when deal_date is not null then concat(lease_tm_legth,'年',lease_tm_legth2,'个月',lease_tm_legth3,'日') end as publish_date, -- 挂牌租期
    case when deal_date is not null then concat(lease_tm_legth,'年',lease_tm_legth2,'个月',lease_tm_legth3,'日') end as lease_tm, -- 租赁期
    deal_rent_prc as deal_price, -- 成交租金价格
    deal_rent_prc_unit_cmnt as deal_rent_unit, -- 成交租金价格单位说明
    lease_area,                -- 出租面积 
    deal_total_price/(lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365)/lease_area as deal_price_metr_y, -- 成交价格（元/平米/年）
    deal_total_price/(lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30)/lease_area as deal_price_metr_m, -- 成交价格（元/平米/月）
    deal_total_price/(lease_tm_legth*365+lease_tm_legth2*30+lease_tm_legth3)/lease_area as deal_rent_metr_unit, -- 成交租金单价(元/平方米/天)  
    deal_total_price/(lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30) as deal_price_m, -- 成交价格（月）
    deal_total_price/(lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365) as deal_price_y, -- 成交价格（年）
    deal_total_price, -- 成交总价（元）
    lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30 as prj_mon_avg_rent, -- 挂牌月平均租金    
    lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365 as prj_year_avg_rent, --挂牌年平均租金
    lease_tm_legth + lease_tm_legth2 / 12 + lease_tm_legth3 / 365 AS lease_tm_y, -- 总年数 租赁期（年）
    lease_tm_legth * 12 + lease_tm_legth2 + lease_tm_legth3 / 30 AS lease_tm_m, -- 总月数 租赁期（月）   
    lease_tm_legth * 365 + lease_tm_legth2 * 30 + lease_tm_legth3 AS lease_tm_d, -- 总天数 租赁期（天）  
    lsct_dt,    -- 起租日
    due_dt,     -- 到期日
    orgn_aavg_rent_prc,          -- 原年平均租金价格（元/年）
    aavg_y_rent,               -- 年平均租金
    COALESCE(aavg_y_rent,0) - COALESCE(orgn_aavg_rent_prc,0) as deal_year_avg_rent, -- 年平均租金增值
    (COALESCE(aavg_y_rent,0) - COALESCE(orgn_aavg_rent_prc,0))/COALESCE(orgn_aavg_rent_prc,0) as init_cntr_prem_rate, -- 较上份合同溢价率
    cntr_sign_dt,              -- 合同签订日期
    deal_date -- 成交日期
from(select 
    bsn_prj_wrd,      --业务项目关键字
    COALESCE(lease_tm_legth,0)as lease_tm_legth,           -- 年 
    COALESCE(lease_tm_legth2,0)as lease_tm_legth2,          -- 月 
    COALESCE(lease_tm_legth3,0)as lease_tm_legth3,         -- 日
    sevly_valut_cd_dsc, --分别计价代码描述
    deal_rent_prc,            -- 成交租金价格
    deal_rent_prc_unit_cmnt,   -- 成交租金价格单位说明
    lease_area,                -- 出租面积
    deal_rent_tot_prc as deal_total_price,    -- 成交总价（元）
    lsct_dt,    -- 起租日
    due_dt,     -- 到期日
    orgn_aavg_rent_prc,          -- 原年平均租金价格（元/年）
    aavg_y_rent,               -- 年平均租金
    cntr_sign_dt,              -- 合同签订日期
    case when deal_date is not null then lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30 
        when deal_date is null and  lse_prd_tp = '2' then lease_tm_legth*12+lease_tm_legth2+lease_tm_legth3/30
        else null end as list_price_std_sq_m,  -- 挂牌价格_标准化（元/平方米/月）
    case when deal_date is not null then lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365 
        when deal_date is null and  lse_prd_tp = '2' then lease_tm_legth+lease_tm_legth2/12+lease_tm_legth3/365
        else null end as list_price_std_sq_y,  -- 挂牌价格_标准化（元/平方米/月）
    deal_date -- 成交日期
from dwd.dwd_ast_lease_deal_rec_fct
where dt = '${dmp_day}' and deal_date is not null
)k1),
tzccz_fyjl_fymx as (
select 
    prj,
    SUM(CASE WHEN cc_rl_dsc = '出租方交易服务会员' THEN cc_amt_yuan ELSE 0 END) AS lessor_agent, -- 出租方会员分佣金额
    SUM(CASE WHEN cc_rl_dsc = '承租方交易服务会员' THEN cc_amt_yuan ELSE 0 END) AS lessee_agent, -- 承租方会员分佣金额
    SUM(CASE WHEN cc_rl_dsc = '北交所' THEN cc_amt_yuan ELSE 0 END) AS cbex_fee_amt -- 北交所服务费
from std.std_bjhl_tzccz_fyjl_fymx_d
where dt = '${dmp_day}'
group by prj
),
tzccz_yxczfxx as (
SELECT 
    project_id, -- 项目ID
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN is_prty_rentw = 1 THEN intnt_rent_nm END)) AS prty_rent_names, -- 有优先承租权的原承租方名称
    COUNT(DISTINCT intnt_rent_nm) AS intnt_rent_count, -- 意向承租方数量
    CONCAT_WS(',', COLLECT_LIST(DISTINCT intnt_rent_nm)) AS intnt_rent_list, -- 所有去重后的意向承租方名称
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN final_qlfy_rst_dsc = '获得资格' THEN intnt_rent_nm END)) AS qlfy_rst_name, -- 合格意向承租方
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN fnl_rent_flg = 1 THEN intnt_rent_nm END)) AS zzczf_name, -- 最终承租方
    CONCAT_WS(',', COLLECT_LIST(CASE WHEN deposit_sts_dsc = '已交纳' THEN cast(mbsh_org as STRING) END)) AS mbsh_org_nm -- 合格意向承租方
FROM 
    std.std_bjhl_tzccz_yxczfxx_d
    where dt = '${dmp_day}' and project_id is not null
GROUP BY 
    project_id -- 按项目ID分组
),
tzccz_zcczxm_fw as (
select
    id,
    k1.posit                  ,--坐落位置
    k2.olvl_admn_rgon_nm as prov,
    k3.olvl_admn_rgon_nm as city,
    k5.olvl_admn_rgon_nm as region_county,
    bscrc_dsc,
    bld_area_sqm,
    CASE 
            WHEN COALESCE(estate_wrnt_no, hs_pty_cert_no, acre_cert_no) IS NULL THEN '无'
            ELSE '有'
        END AS bdcqzt, -- 不动产权证（有/无）
    hs_use_crn_sttn_dsc,
    prop, --产权人
    mrtg_cntr --抵押合同
from std.std_bjhl_tzccz_zcczxm_fw_d k1
left join (select olvl_admn_rgon_cd,olvl_admn_rgon_nm from dim.dim_admn_rgon_info
           where dt = '${dmp_day}' and edw_dt_src_tbl = 'livebos.txzqydm')k2
on k1.prov = k2.olvl_admn_rgon_cd
left join (select olvl_admn_rgon_cd,olvl_admn_rgon_nm from dim.dim_admn_rgon_info
           where dt = '${dmp_day}' and edw_dt_src_tbl = 'livebos.txzqydm')k3
on k1.city = k3.olvl_admn_rgon_cd
left join (select olvl_admn_rgon_cd,olvl_admn_rgon_nm from dim.dim_admn_rgon_info
           where dt = '${dmp_day}' and edw_dt_src_tbl = 'livebos.txzqydm')k5
on k1.region_county = k5.olvl_admn_rgon_cd
where dt = '${dmp_day}'
 ),
tzccz_ywdd as (
select 
    prj, 
    SUM(CASE WHEN py_side_rl_dsc = '出租方' THEN to_acc_amt_yuan ELSE 0 END) AS lessor_txn_serv_fee, -- 出租方交易服务费
    SUM(CASE WHEN py_side_rl_dsc = '承租方' THEN to_acc_amt_yuan ELSE 0 END) AS rent_txn_serv_fee -- 承租方交易服务费
from std.std_bjhl_tzccz_ywdd_d
where dt = '${dmp_day}' and ordr_tp_dsc ='交易服务费' and ordr_sts_dsc = '支付成功' and py_side_rl_dsc in ('出租方','承租方')
group by prj
),
tzccz_yxczfxx_1 as (
SELECT 
    k1.project_id,
    concat_ws(',', collect_set(cast(k2.usr_nm AS STRING))) AS rent_txn_serv_mem -- 承租方受托交易服务会员
FROM (
    SELECT DISTINCT project_id, mbsh_org FROM std.std_bjhl_tzccz_yxczfxx_d WHERE dt = '${dmp_day}' AND deposit_sts_dsc = '已交纳' AND mbsh_org IS NOT NULL
) k1
INNER JOIN (
    SELECT DISTINCT sub_org, usr_nm FROM std.std_bjhl_tuser_d WHERE dt = '${dmp_day}' AND sub_org IS NOT NULL AND usr_nm IS NOT NULL
) k2 ON k1.mbsh_org = k2.sub_org
GROUP BY k1.project_id
),
tbid_fwhydy as (
SELECT 
    k1.org_id,
    concat_ws(',', collect_set(cast(k2.usr_nm AS STRING))) AS lessor_txn_serv_mem -- 出租方受托交易服务会员
FROM (
    SELECT DISTINCT org_id, rltv FROM std.std_bjhl_tbid_fwhydy_d WHERE dt = '${dmp_day}'  -- 服务会员对应关系
) k1
INNER JOIN (
    SELECT DISTINCT id, usr_nm FROM std.std_bjhl_tuser_d WHERE dt = '${dmp_day}'  -- 用户管理
) k2 ON k1.rltv = k2.id
GROUP BY k1.org_id
)
SELECT
    A.prj_id AS project_id,                                    -- 项目编号
    A.prj_nm AS project_name,                                  -- 项目名称
    A.ast_cgy_dsc AS asset_cate,                                    -- 资产类别
    A.prj_sts_dsc AS project_status,                                -- 项目当前状态_系统值
    CASE '当前日期' BETWEEN A.esr_strt_dt AND A.esr_end_dt THEN '是' ELSE '否' END AS is_listing,                                    -- 是否披露中
    CASE WHEN a.deal_date IS NOT NULL OR a.deal_date != '' THEN '是' ELSE '否' END AS is_dealed,                                     -- 是否已成交
    A.prj_pnp AS prj_manager,                                   -- 项目负责人
    A.prj_blng_dept AS prj_department,                                -- 项目所属部门
    A.prj_clm_dept_nm AS prj_department_claim,                          -- 项目认领部门
    A.rent_txn_serv_mem AS lessee_member,                                 -- 承租方受托交易服务会员
    A.lessor_txn_serv_mem AS lessor_member,                                 -- 出租方受托交易服务会员
    A.is_nal_hs_rent AS is_state_lease,                                -- 是否国有房屋出租
    CASE WHEN ast_src_entp_nm_3 = '省级国资委监管' AND ast_src_entp_nm_4 = '北京市国资委'
              AND plan_lease_area <= 1000 AND E.deal_year_avg_rent <= 1000000
              THEN '非强制进场'
         WHEN ast_src_entp_nm_3 = '省级国资委监管' AND ast_src_entp_nm_4 = '北京市国资委'
              AND plan_lease_area > 1000 AND E.deal_year_avg_rent > 1000000
              THEN '非强制进场'
         ELSE NULL END is_mandatory_entry,                            -- 是否强制进场_市属
    A.is_exst_prty_rentw AS is_priority_right,                             -- 是否存在优先承租权
    A.prty_rent_names AS priority_lessee_nm,                            -- 有优先承租权的原承租方名称
    B.SFYYFYXX [ods有但是std没] is_operate,                                    -- 是否运营房源信息
    B.rltv_pre_esr [确定判断逻辑] AS is_pre_list,                                   -- 是否有预披露项目
    A.deposit_yuan AS margin_money,                                  -- 保证金金额（元）
    A.pcolt_lhder_cnt AS intend_lessee_cnt,                             -- 拟征集承租方个数
    A.pick_mod_dsc AS pick_method,                                   -- 遴选方式
    B.is_req_sgn as is_esign,                                      -- 是否需要电签
    C.posit AS location_house,                                -- 【房屋】坐落位置（房屋）
    C.prov AS province_house,                                -- 【房屋】省份
    C.city AS city_house,                                    -- 【房屋】城市
    C.region_county AS district_house,                                -- 【房屋】市辖区
    street_house,                                  -- 【房屋】街道
    C.bscrc_dsc AS biz_district_house,                            -- 【房屋】商圈
    '*' AS dt_address_house,                              -- 【房屋】详细地址
    '*' AS lng_house,                                     -- 【房屋】经度
    '*' AS lat_house,                                     -- 【房屋】纬度
    C.bld_area_sqm AS area_house,                                    -- 【房屋】建筑面积(平方米)
    A.use_rqmt_cd_dsc AS usage_house,                                   -- 【房屋】房屋使用用途
    C.hs_use_crn_sttn_dscusage_st_house,                                -- 【房屋】使用现状
    land_right_house,                              -- 【房屋】土地使用权性质
    A.ast_cgy_dsc AS asset_class_house,                             -- 【房屋】资产分类
    C.prop AS owner_house,                                   -- 【房屋】产权人
    CASE WHEN c.mrtg_cntr IS NOT NULL OR c.mrtg_cntr != '' THEN '是' ELSE '否' END AS is_mortgage_house,               -- 【房屋】是否有抵押
    CASE WHEN c.mrtg_cntr IS NOT NULL OR c.mrtg_cntr != '' THEN '是' ELSE '否' END AS is_coowned_house,                -- 【房屋】是否共有
    F.loct_lo AS location_land,                                 -- 【土地】坐落位置
    F.land_area AS area_land,                                     -- 【土地】土地面积（平方米）
    F.use AS usage_land,                                    -- 【土地】用途
    F.tp AS type_land,                                     -- 【土地】类型
    F.use_yrs AS usage_period_house,                            -- 【土地】使用年限
    F.used_yrs AS used_period_house,                             -- 【土地】已用年限
    CASE WHEN G.id IS NOT NULL THEN '是' ELSE '否' END AS is_machine,                                    -- 【机械设备】是否有机械设备
    G.mc AS machine_nm,                                    -- 【机械设备】机械设备名称
    CASE WHEN H.id IS NOT NULL THEN '是' ELSE '否' END AS is_vehicle,                                    -- 【交通运输工具】是否有交通运输工具
    CASE WHEN H.sffxq = '1' THEN '是' ELSE '否' END AS is_aircraft,                                   -- 【交通运输工具】是否飞行器 （后面看下ods表sffxq具体值）
    H.sl AS vehicle_num,                                   -- 【交通运输工具】交通运输工具数量
    CASE WHEN I.id IS NOT NULL THEN '是' ELSE '否' END AS is_other_asset,                                -- 【其他资产】是否有其他资产
    I.mc AS other_asset_nm,                                -- 【其他资产】其他资产名称
    A.esr_strt_dt as  list_start_date,                               -- 当前挂牌开始日期
    A.esr_end_dt as list_end_date,                                 -- 当前挂牌结束日期
    SUBSTR(A.esr_strt_dt, 1, 4) AS list_start_year,                               -- 当前挂牌开始年份
    SUBSTR(A.esr_end_dt, 1, 4) AS list_end_year,                                 -- 当前挂牌结束年份
    SUBSTR(A.esr_strt_dt, 1, 7) AS list_start_ym,                                 -- 当前挂牌开始年月
    SUBSTR(A.esr_end_dt, 1, 7) AS list_end_ym,                                   -- 当前挂牌结束年月
    365 AS annual_days,                                   -- 标准化年天数（天）
    30 AS monthly_days,                                  -- 标准化月天数（天）
    A.is_resp_val_dsc AS is_sep_price,                                  -- 是否分别计价
    A.rent_lst_prc AS list_price_uni,                                -- 挂牌价格_统一计价录入值
    A.prj_prc_unit_dsc AS list_price_unit_uni,                           -- 挂牌价格单位_统一计价录入值
    map(FBJJGPJ,FBJJGPJDW) as list_price_sep,                                -- 挂牌价格_分别计价录入值
    list_price_std_sq_d,                           -- 挂牌价格_标准化（元/平方米/天）
    E.list_price_std_sq_m AS list_price_std_sq_m,                           -- 挂牌价格_标准化（元/平方米/月）
    E.list_price_std_sq_y AS list_price_std_sq_y,                           -- 挂牌价格_标准化（元/平方米/年）
    list_price_std_d,                              -- 挂牌价格_标准化（元/天）
    E.list_price_std_sq_m * A.plan_lease_area AS list_price_std_m,                              -- 挂牌价格_标准化（元/月）
    E.list_price_std_sq_y * A.plan_lease_area AS list_price_std_y,                              -- 挂牌价格_标准化（元/年）
    B.avrg_mo_rnt_fee_yuan_mo as list_price_sys_m, -- 平均月租金_系统值（元/月）
    B.avrg_yr_rnt_fee_yuan_yr as list_price_sys_y, -- 平均年租金_系统值（元/年）
    B.avrg_yr_rnt_fee_yuan_yr / 365 as list_price_sys_d, -- 平均日租金_系统计算值（元/天）
    -- 平均日租金_系统计算值（元/平方米/天）：平均年租金_系统值（元/年）/（拟出租面积*365）
    CASE
        WHEN A.plan_lease_area > 0 THEN B.avrg_yr_rnt_fee_yuan_yr / (A.plan_lease_area * 365)
        ELSE NULL
    END AS list_price_sys_std_sq_d,

    -- 平均月租金_系统计算值（元/平方米/月）：平均月租金_系统值（元/月）/拟出租面积
    CASE
        WHEN A.plan_lease_area > 0 THEN B.avrg_mo_rnt_fee_yuan_mo / A.plan_lease_area
        ELSE NULL
    END AS list_price_sys_std_sq_m,

    -- 平均年租金_系统计算值（元/平方米/年）：平均年租金_系统值（元/年）/拟出租面积
    CASE
        WHEN A.plan_lease_area > 0 THEN B.avrg_yr_rnt_fee_yuan_yr / A.plan_lease_area
        ELSE NULL
    END AS list_price_sys_std_sq_y,
    A.plan_lease_area as lease_area,                                    -- 拟出租面积（平方米）
    lease_prd_type,                                -- 租赁期类型
    lease_prd_range_type,                          -- 租赁期区间类型
    lease_prd_ent1,                                -- 挂牌租赁期_录入值1
    lease_prd_ent2,                                -- 挂牌租赁期_录入值2
    lease_prd_end_date,                            -- 挂牌租赁期_截止日期
    lease_prd_std_d,                               -- 挂牌租赁期_标准化（天）
    lease_prd_std_m,                               -- 挂牌租赁期_标准化（月）
    lease_prd_std_y,                               -- 挂牌租赁期_标准化（年）
    list_total_price,                              -- 挂牌总价(元)
    is_sep_appraise,                               -- 是否分别估价
    appr_price_uni,                                -- 租金估价_统一估价录入值
    appr_price_unit_uni,                           -- 估价单位_统一估价录入值
    appr_price_sep,                                -- 租金估价_分别估价录入值
    appr_price_std_sq_d,                           -- 租金估价_标准化（元/平方米/天）
    appr_total_price,                              -- 租金估价_总价(元)
    re_list_num,                                   -- 重新披露次序
    is_last_list,                                  -- 是否最后一次披露
    f_list_start_date,                             -- 初次挂牌开始日期
    f_list_end_date,                               -- 初次挂牌结束日期
    f_list_start_year,                             -- 初次挂牌开始年份
    f_list_end_year,                               -- 初次挂牌结束年份
    f_list_start_ym,                               -- 初次挂牌开始年月
    f_list_end_ym,                                 -- 初次挂牌结束年月
    f_list_price_std_sq_d,                         -- 初次挂牌价格_标准化（元/平方米/天）
    reduce_price_std_sq_d,                         -- 降价金额_标准化（元/平方米/天）
    is_extend,                                     -- 是否手工延牌
    free_prd_type,                                 -- 免租期类型
    free_prd_ent1,                                 -- 免租期_录入值1
    free_prd_ent2,                                 -- 免租期_录入值2
    free_prd_std_d,                                -- 免租期_标准化（天）
    deal_date,                                     -- 成交日期
    deal_price_type,                               -- 成交计价方式
    deal_price_uni,                                -- 成交租金价格_统一计价录入值
    deal_price_unit_uni,                           -- 成交租金价格单位_统一计价录入值
    deal_price_remark,                             -- 成交租金价格备注
    deal_price_sep,                                -- 成交租金价格_分别计价录入值
    deal_price_std_sq_d,                           -- 成交租金价格_标准化（元/平方米/天）
    deal_total_price,                              -- 成交租金总价_录入值（元）
    deal_lease_area,                               -- 出租面积_成交录入（平方米）
    deal_lease_prd_ent,                            -- 租赁时长_成交录入
    deal_lease_prd_std_d,                          -- 租赁时长_成交标准化（天）
    premium_vs_list_price,                         -- 溢价金额_对比挂牌单价（元/平方米/天）
    premium_vs_list_total_price,                   -- 溢价金额_对比挂牌总价（元）
    premium_rate_vs_list_price,                    -- 溢价率_对比挂牌单价
    premium_vs_appr_price,                         -- 溢价金额_对比评估单价（元/平方米/天）
    premium_vs_appr_total_price,                   -- 溢价金额_对比评估总价（元）
    premium_rate_vs_appr_price,                    -- 溢价率_对比评估单价
    deal_price_precontract,                        -- 上份合同的成交租金价格（元/平方米/天）
    increase_vs_precontract,                       -- 较上份合同增值（元/平方米/天）
    increase_rate_vs_precontract,                  -- 较上份合同增值率
    actual_deal_methond,                           -- 实际交易方式
    bid_type,                                      -- 网络竞价类型
    sign_date_contract,                            -- 合同签订日期
    effective_date_contract,                       -- 合同生效日期
    lessor_name,                                   -- 出租方名称
    lessor_province,                               -- 出租方所在省
    lessor_city,                                   -- 出租方所在市
    lessor_district,                               -- 出租方所在区
    lessor_reg_addr,                               -- 出租方注册地
    lessor_type,                                   -- 出租方类型
    asset_source_l2,                               -- 资产来源(2级)
    asset_source_l3,                               -- 资产来源(3级)
    asset_source_l4,                               -- 资产来源(4级)
    asset_source_l5,                               -- 资产来源(5级)
    parent_group,                                  -- 所属集团
    approval_unit,                                 -- 项目批准单位
    enterprise_tier_num,                           -- 企业层级数量
    asset_source_ent_l1,                           -- 资产来源（企业1级）
    asset_source_ent_l2,                           -- 资产来源（企业2级）
    asset_source_ent_l3,                           -- 资产来源（企业3级）
    asset_source_ent_l4,                           -- 资产来源（企业4级）
    asset_source_ent_l5,                           -- 资产来源（企业5级）
    lessor_eco_nature,                             -- 出租方经济性质
    lessor_industry_type,                          -- 出租方所属行业类型
    lessor_industry,                               -- 出租方所属行业
    lessor_biz_scope,                              -- 出租方经营范围
    reg_intend_lessee_cnt,                         -- 报名的意向承租方数量
    bid_intend_lessee_cnt,                         -- 具备竞价资格的意向承租方数量
    lessee_name,                                   -- 承租方名称
    lessee_type,                                   -- 承租方类型
    lessee_province,                               -- 承租方所在省
    lessee_district,                               -- 承租方所在区
    lessee_name_second_bid,                        -- 竞价排名第二的意向承租方名称
    lessee_offer_second_bid,                       -- 竞价排名第二的意向承租方报价（元/平方米/天）
    lessee_name_third_bid,                         -- 竞价排名第三的意向承租方名称
    lessee_offer_third_bid,                        -- 竞价排名第三的意向承租方报价（元/平方米/天）
    total_revenue,                                 -- 北交所总收入（元）
    net_revenue,                                   -- 北交所净收入（元）
    lessor_service_fee,                            -- 出租方服务费金额（元）
    lessee_service_fee,                            -- 承租方服务费金额（元）
    lessor_memb_comm,                              -- 出租方会员分佣金额（元）
    lessee_memb_comm,                              -- 承租方会员分佣金额（元）
    intend_lessee_mmb_comm,                        -- 意向承租方会员分佣金额（元）
    remain_pay_method,                             -- 剩余价款结算方式
    is_foreign_curr_settle,                        -- 是否外币结算
    is_mm_handle_changed,                          -- 保证金处置方式是否有变更
    mm_handle_method,                              -- 保证金处置方式
    is_mm_convert_rent,                            -- 保证金是否转成交租金总价
    prj_entry_date,                                -- 项目录入日期
    ulti_lessee_confirm_date,                      -- 确认最终承租方日期
    deal_cycle_ent_list_nd,                        -- 成交周期_录入至披露_自然日
    deal_cycle_list_conf_nd,                       -- 成交周期_披露至确认最终承租方_自然日
    deal_cycle_conf_deal_nd,                       -- 成交周期_确认最终承租方至成交_自然日
    deal_cycle_ent_deal_nd,                        -- 成交周期_录入至成交_自然日
    deal_cycle_list_deal_nd,                       -- 成交周期_披露至成交_自然日
    deal_cycle_ent_list_wd,                        -- 成交周期_录入至披露_工作日
    deal_cycle_list_conf_wd,                       -- 成交周期_披露至确认最终承租方_工作日
    deal_cycle_conf_deal_wd,                       -- 成交周期_确认最终承租方至成交_工作日
    deal_cycle_ent_deal_wd,                        -- 成交周期_录入至成交_工作日
    deal_cycle_list_deal_wd,                       -- 成交周期_披露至成交_工作日
    prj_view_cnt,                                  -- 项目围观次数
    prj_follow_cnt,                                -- 项目关注用户数
    rent_adjust_method,                            -- 租金调整方式
    list_price_notes,                              -- 租金挂牌价补充说明
    rent_mm_pay_require,                           -- 租金及押金支付要求
    is_rent_cover_other_fees,                      -- 租金挂牌价是否含其他费用
    lessee_resp_fees,                              -- 承租方需承担费用
    other_list_items                               -- 其他披露事项
FROM tzccz_zcczxm  A --k1
LEFT JOIN std.std_bjhl_tzccz_zcczxm_d B
ON A.project_code = B.project_code
LEFT JOIN tzccz_zcczxm_fw C  -- K7 房屋信息
ON A.bsn_prj_id = C.id
LEFT JOIN tzccz_czfxx D --k2 出租方信息
on A.bsn_prj_id = D.project_id
left join tzccz_cjjl  E -- k3 成交记录
on A.bsn_prj_id = E.bsn_prj_wrd
left join std.std_bjhl_tzccz_zcczxm_td_d F --k8 土地
on A.bsn_prj_id = F.land_id
left join std.std_bjhl_tzccz_zcczxm_jxsb_d G -- 机械设备
on A.bsn_prj_id = G.id and G.dt = '${dmp_day}' -- 条件待定
left join std.std_bjhl_tzccz_zcczxm_jtgj_d H --交通运输工具
on A.bsn_prj_id = H.id and H.dt = '${dmp_day}'  -- 条件待定
left join std.std_bjhl_tzccz_zcczxm_qt_d I --其他资产
on A.bsn_prj_id = I.id and I.dt = '${dmp_day}'  -- 条件待定


